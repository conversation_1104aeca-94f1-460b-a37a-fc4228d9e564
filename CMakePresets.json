{"version": 3, "configurePresets": [{"name": "default", "hidden": true, "generator": "Ninja", "binaryDir": "${sourceDir}/build/${presetName}", "toolchainFile": "${sourceDir}/cmake/gcc-arm-none-eabi.cmake", "cacheVariables": {}}, {"name": "Debug", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}, {"name": "Release", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}], "buildPresets": [{"name": "Debug", "configurePreset": "Debug"}, {"name": "Release", "configurePreset": "Release"}]}