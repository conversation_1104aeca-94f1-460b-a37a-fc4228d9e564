-- The C compiler identification is GNU 13.3.1
-- The CXX compiler identification is GNU 13.3.1
-- Checking whether C compiler has -isysroot
-- Checking whether C compiler has -isysroot - yes
-- Checking whether C compiler supports OSX deployment target flag
-- Checking whether C compiler supports OSX deployment target flag - no
-- Detecting C compiler <PERSON><PERSON> info
-- Detecting C compiler AB<PERSON> info - failed
-- Check for working C compiler: /Volumes/Applications/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc
-- Check for working C compiler: /Volumes/Applications/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc - broken
CMake Error at /Volumes/Applications/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:67 (message):
  The C compiler

    "/Volumes/Applications/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc"

  is not able to compile a simple test program.export CMAKE_OSX_ARCHITECTURES=x86_64


  It fails with the following output:

    Change Dir: '/private/var/folders/lm/tmxn7r012t1dv_9flxb_mv_40000gn/T/cmake_check_environment2/_build9558103096637722393/CMakeFiles/CMakeScratch/TryCompile-L9tdMq'

    Run Build Command(s): /Volumes/Applications/opt/ST/STM32CubeCLT_1.19.0/Ninja/bin/ninja -v cmTC_dc948
    [1/2] /Volumes/Applications/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc   -arch x86_64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX14.2.sdk -fdiagnostics-color=always -o CMakeFiles/cmTC_dc948.dir/testCCompiler.c.o -c /private/var/folders/lm/tmxn7r012t1dv_9flxb_mv_40000gn/T/cmake_check_environment2/_build9558103096637722393/CMakeFiles/CMakeScratch/TryCompile-L9tdMq/testCCompiler.c
    FAILED: CMakeFiles/cmTC_dc948.dir/testCCompiler.c.o
    /Volumes/Applications/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc   -arch x86_64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX14.2.sdk -fdiagnostics-color=always -o CMakeFiles/cmTC_dc948.dir/testCCompiler.c.o -c /private/var/folders/lm/tmxn7r012t1dv_9flxb_mv_40000gn/T/cmake_check_environment2/_build9558103096637722393/CMakeFiles/CMakeScratch/TryCompile-L9tdMq/testCCompiler.c
    arm-none-eabi-gcc: error: unrecognized command-line option '-arch'; did you mean '-march='?
    ninja: build stopped: subcommand failed.





  CMake will not be able to correctly generate this project.
Call Stack (most recent call first):
  CMakeLists.txt:2 (project)


-- Configuring incomplete, errors occurred!

Error code: 1